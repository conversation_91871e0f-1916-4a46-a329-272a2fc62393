# Query Processing & Search Systems
## Advanced Query Classification and Multi-Stage Retrieval Architecture

### Table of Contents
1. [Query Classification System](#query-classification-system)
2. [Processing Pipeline Architecture](#processing-pipeline-architecture)
3. [Multi-Stage Retrieval Process](#multi-stage-retrieval-process)
4. [Query Type Processing Flows](#query-type-processing-flows)
5. [Search and Relevance Assessment](#search-and-relevance-assessment)

---

## Query Classification System

Augment employs a sophisticated multi-model approach to classify incoming queries into three primary categories, enabling optimized processing strategies for different types of developer questions.

### Multi-Model Classification Architecture

```python
class QueryClassificationSystem:
    def __init__(self):
        # Intent classification model
        self.intent_classifier = TransformerModel(
            model_name="augment/query-intent-classifier-v2",
            architecture="bert-base-uncased",
            fine_tuned_on="developer_query_dataset_50k",
            accuracy=0.94
        )
        
        # Specificity analyzer
        self.specificity_analyzer = SpecificityModel(
            features=["entity_count", "technical_terms", "file_references", "function_names"],
            algorithm="gradient_boosting",
            accuracy=0.91
        )
        
        # Context scope detector
        self.scope_detector = ScopeAnalysisModel(
            embedding_model="sentence-transformers/all-mpnet-base-v2",
            similarity_threshold=0.75,
            scope_categories=["file_level", "function_level", "system_level", "architectural_level"]
        )
        
    def classify_query(self, query: str, codebase_context: CodebaseContext) -> QueryClassification:
        # Extract linguistic features
        linguistic_features = self._extract_linguistic_features(query)
        
        # Classify intent
        intent_result = self.intent_classifier.predict(query)
        
        # Analyze specificity
        specificity_score = self.specificity_analyzer.analyze(linguistic_features)
        
        # Determine scope
        scope_result = self.scope_detector.analyze(query, codebase_context)
        
        # Combine results using weighted ensemble
        classification = self._ensemble_classification(
            intent_result, specificity_score, scope_result
        )
        
        return QueryClassification(
            type=classification.query_type,
            confidence=classification.confidence,
            intent=intent_result.primary_intent,
            scope=scope_result.detected_scope,
            specificity=specificity_score,
            processing_strategy=self._determine_processing_strategy(classification)
        )
```

### Query Type Definitions

**1. General Queries (System-Level Understanding)**
- **Characteristics**: Broad architectural questions, system-wide patterns
- **Examples**: "How does this codebase handle data persistence?", "What's the overall architecture?"
- **Specificity Score**: 0.0 - 0.3
- **Scope**: System-wide or architectural-level
- **Processing Strategy**: Comprehensive multi-component analysis (95/113 sub-steps)
- **Processing Time**: 41-60 seconds
- **Activated Components**: All 7 major analysis components

**2. Semi-General Queries (Feature/Domain-Specific)**
- **Characteristics**: Focused on specific functionality or domain area
- **Examples**: "How does user authentication work?", "How are payments processed?"
- **Specificity Score**: 0.3 - 0.7
- **Scope**: Feature or domain-level
- **Processing Strategy**: Targeted component analysis (64/113 sub-steps)
- **Processing Time**: 17-27 seconds
- **Activated Components**: 5 major analysis components

**3. Focused Queries (Specific Code Elements)**
- **Characteristics**: Target specific functions, classes, or files
- **Examples**: "What does authenticate_user function do?", "How is UserService.create_user implemented?"
- **Specificity Score**: 0.7 - 1.0
- **Scope**: File or function-level
- **Processing Strategy**: Direct code analysis (34/113 sub-steps)
- **Processing Time**: 5.5-10 seconds
- **Activated Components**: 3 major analysis components

## Processing Pipeline Architecture

### Core Pipeline Components

```python
class QueryProcessingPipeline:
    def __init__(self):
        self.query_classifier = QueryClassificationSystem()
        self.context_retrieval_engine = ContextRetrievalEngine()
        self.semantic_analyzer = SemanticAnalyzer()
        self.context_ranker = ContextRanker()
        self.package_generator = LLMPackageGenerator()
        self.performance_monitor = PerformanceMonitor()
        
    async def process_query(self, query: str, codebase_context: CodebaseContext) -> QueryResult:
        start_time = time.time()
        
        # Step 1: Classify query type and intent
        classification = self.query_classifier.classify_query(query, codebase_context)
        
        # Step 2: Retrieve relevant context based on classification
        raw_context = await self.context_retrieval_engine.retrieve_context(
            query=query,
            classification=classification,
            codebase_context=codebase_context
        )
        
        # Step 3: Perform semantic analysis and enrichment
        enriched_context = await self.semantic_analyzer.analyze_and_enrich(
            raw_context, query, classification
        )
        
        # Step 4: Rank and select optimal context
        ranked_context = self.context_ranker.rank_and_select(
            enriched_context, query, classification
        )
        
        # Step 5: Generate LLM-friendly package
        final_package = self.package_generator.generate_package(
            ranked_context, query, classification
        )
        
        # Step 6: Log performance metrics
        processing_time = time.time() - start_time
        self.performance_monitor.log_query_processing(
            query, classification, processing_time, final_package
        )
        
        return QueryResult(
            package=final_package,
            classification=classification,
            processing_time_ms=processing_time * 1000,
            context_sources=ranked_context.sources,
            confidence_score=ranked_context.overall_confidence
        )
```

### Component Activation Matrix

The following matrix shows which of the 113 sub-steps are activated for each query type:

| Component | Sub-Steps | General Queries | Semi-General | Focused |
|-----------|-----------|----------------|--------------|---------|
| Code Snippets | 1.1-1.12 | All (12/12) | Selective (8/12) | Targeted (6/12) |
| Dependencies | 2.1-2.14 | All (14/14) | Most (11/14) | Limited (5/14) |
| Architectural Patterns | 3.1-3.15 | All (15/15) | Relevant (9/15) | Minimal (3/15) |
| Quality Metrics | 4.1-4.15 | Most (12/15) | Selective (7/15) | Basic (4/15) |
| Change History | 5.1-5.17 | Selective (8/17) | Limited (5/17) | Minimal (3/17) |
| Cross-File Relationships | 6.1-6.20 | All (20/20) | Most (15/20) | Local (8/20) |
| Test Information | 7.1-7.20 | Most (14/20) | Relevant (9/20) | Limited (5/20) |
| **Total Activated** | **113** | **95/113 (84%)** | **64/113 (57%)** | **34/113 (30%)** |

## Multi-Stage Retrieval Process

### Stage 1: Candidate Generation

```python
def generate_candidates(self, processed_query, top_k=1000):
    candidates = []
    
    # Vector similarity search
    vector_candidates = self.vector_db.similarity_search(
        processed_query["embedding"], 
        top_k=top_k//2
    )
    
    # Graph-based traversal for related code
    graph_candidates = self.graph_db.find_related_nodes(
        processed_query["entities"],
        max_depth=3,
        relationship_types=["calls", "inherits", "imports", "references"]
    )
    
    # Keyword-based fallback
    keyword_candidates = self._keyword_search(
        processed_query["expanded_terms"]
    )
    
    return self._merge_candidates(
        vector_candidates, graph_candidates, keyword_candidates
    )
```

### Stage 2: Relevance Scoring

**Multi-Factor Relevance Algorithm:**
- **Semantic Similarity**: 40% weight - Understanding of code functionality
- **Structural Relevance**: 25% weight - Architectural importance in system
- **Contextual Fit**: 20% weight - Relevance to current development context
- **Recency Boost**: 10% weight - Recent changes and activity
- **Popularity Signal**: 5% weight - Frequency of access and modification

## Query Type Processing Flows

### General Query Processing Flow

**Activation Pattern**: Comprehensive multi-component analysis (95/113 sub-steps)

```python
class GeneralQueryProcessor:
    def __init__(self):
        self.architectural_analyzer = ArchitecturalAnalyzer()
        self.pattern_detector = SystemPatternDetector()
        self.relationship_mapper = CrossFileRelationshipMapper()
        
    async def process_general_query(self, query: str, codebase: CodebaseContext) -> GeneralQueryResult:
        # Phase 1: System-wide architectural analysis (15-20s)
        architectural_context = await self._analyze_system_architecture(codebase)
        
        # Phase 2: Pattern detection across all components (10-15s)
        system_patterns = await self._detect_system_patterns(codebase, query)
        
        # Phase 3: Cross-file relationship mapping (8-12s)
        relationship_graph = await self._map_cross_file_relationships(codebase)
        
        # Phase 4: Quality and dependency analysis (5-8s)
        quality_metrics = await self._analyze_system_quality(codebase)
        dependency_graph = await self._build_dependency_graph(codebase)
        
        # Phase 5: Context synthesis and ranking (3-5s)
        synthesized_context = self._synthesize_context(
            architectural_context, system_patterns, relationship_graph,
            quality_metrics, dependency_graph, query
        )
        
        return GeneralQueryResult(
            context=synthesized_context,
            processing_phases=5,
            total_processing_time="41-60s",
            activated_components=7,
            confidence_score=0.87
        )
```

### Semi-General Query Processing Flow

**Activation Pattern**: Targeted component analysis (64/113 sub-steps)

```python
class SemiGeneralQueryProcessor:
    def __init__(self):
        self.domain_analyzer = DomainSpecificAnalyzer()
        self.feature_mapper = FeatureMapper()
        self.semantic_clusterer = SemanticClusterer()

    async def process_semi_general_query(self, query: str, codebase: CodebaseContext) -> SemiGeneralQueryResult:
        # Phase 1: Domain identification and scoping (3-5s)
        domain_scope = await self._identify_domain_scope(query, codebase)

        # Phase 2: Feature-specific code discovery (5-8s)
        relevant_code = await self._discover_feature_code(domain_scope, codebase)

        # Phase 3: Semantic clustering and relationship analysis (4-6s)
        code_clusters = await self._cluster_related_code(relevant_code, query)

        # Phase 4: Dependency and pattern analysis within scope (3-5s)
        scoped_dependencies = await self._analyze_scoped_dependencies(code_clusters)
        feature_patterns = await self._detect_feature_patterns(code_clusters)

        # Phase 5: Context ranking and selection (2-3s)
        ranked_context = self._rank_and_select_context(
            code_clusters, scoped_dependencies, feature_patterns, query
        )

        return SemiGeneralQueryResult(
            context=ranked_context,
            processing_phases=5,
            total_processing_time="17-27s",
            activated_components=5,
            confidence_score=0.91
        )
```

### Focused Query Processing Flow

**Activation Pattern**: Direct code analysis (34/113 sub-steps)

```python
class FocusedQueryProcessor:
    def __init__(self):
        self.code_locator = DirectCodeLocator()
        self.local_analyzer = LocalContextAnalyzer()
        self.signature_analyzer = SignatureAnalyzer()

    async def process_focused_query(self, query: str, codebase: CodebaseContext) -> FocusedQueryResult:
        # Phase 1: Direct code location (1-2s)
        target_code = await self._locate_target_code(query, codebase)

        # Phase 2: Local context analysis (2-3s)
        local_context = await self._analyze_local_context(target_code, codebase)

        # Phase 3: Signature and usage analysis (1-2s)
        signature_info = await self._analyze_signature_and_usage(target_code, codebase)

        # Phase 4: Immediate dependency analysis (1-2s)
        immediate_deps = await self._analyze_immediate_dependencies(target_code)

        # Phase 5: Context packaging (0.5-1s)
        packaged_context = self._package_focused_context(
            target_code, local_context, signature_info, immediate_deps
        )

        return FocusedQueryResult(
            context=packaged_context,
            processing_phases=5,
            total_processing_time="5.5-10s",
            activated_components=3,
            confidence_score=0.95
        )
```

## Search and Relevance Assessment

### Advanced Search Methodology

The Augment Agent employs sophisticated search strategies that go beyond simple keyword matching:

1. **Semantic Search**: Understanding the intent and meaning behind queries
2. **Contextual Search**: Considering the current development context and recent changes
3. **Architectural Search**: Finding code based on architectural patterns and relationships
4. **Quality-Aware Search**: Prioritizing high-quality, well-documented code examples
5. **Temporal Search**: Considering recency and change frequency in relevance scoring

### Relevance Assessment Framework

**Multi-Dimensional Relevance Scoring:**
- **Functional Relevance**: How well the code matches the functional requirements
- **Architectural Relevance**: How important the code is to the overall system architecture
- **Quality Relevance**: Code quality metrics and maintainability scores
- **Contextual Relevance**: Relevance to current development tasks and recent changes
- **Usage Relevance**: How frequently the code is accessed and modified

---

### Step-by-Step Processing Example: Semi-General Query

**Example Query**: "How does user authentication work in this system?"

#### Step 1: Natural Language Query Understanding

```python
# User asks: "How does user authentication work in this system?"

query_processor = NaturalLanguageQueryProcessor()
parsed_query = query_processor.understand_query(
    query="How does user authentication work in this system?",
    intent_classification=True,
    entity_extraction=True
)

# Result:
# - Primary intent: "code_understanding"
# - Secondary intent: "architecture_analysis"
# - Key entities: ["user", "authentication", "system"]
# - Query type: "semi_general" (not specific file/function)
```

#### Step 2: Context Retrieval from 113-Sub-Step Analysis

```python
context_engine = ContextEngine()
relevant_context = context_engine.retrieve_context(
    query=parsed_query,
    analysis_components=[
        "code_snippets",      # Find auth-related functions
        "dependencies",       # Find auth service dependencies
        "architectural_patterns", # Find auth patterns
        "cross_file_relationships" # Find auth flow across files
    ]
)
```

#### Step 3: Intelligent Context Selection

```python
context_selector = IntelligentContextSelector()
selected_context = context_selector.select_optimal_context(
    all_matches=relevant_context,
    query_intent=parsed_query.intent,
    token_budget=2000,  # LLM context window limit
    relevance_threshold=0.7
)

# Ranking algorithm considers:
# 1. Semantic similarity to query (40% weight)
# 2. Architectural importance (25% weight)
# 3. Code quality/completeness (20% weight)
# 4. Recent activity/changes (15% weight)
```

#### Step 4: Final LLM-Friendly Package Generation

```python
# Final LLM-friendly package:
context_package = {
    "critical_entities": [
        {
            "name": "authenticate_user",
            "type": "function",
            "file": "auth/authentication.py",
            "signature": "def authenticate_user(username: str, password: str) -> Optional[str]",
            "purpose": "Main authentication handler",
            "dependencies": ["get_user", "generate_token"]
        }
    ],
    "architectural_context": {
        "auth_flow": "JWT-based authentication with service layer pattern",
        "security_model": "Token-based with role validation",
        "integration_points": ["API gateway", "middleware", "database"]
    },
    "code_snippets": [
        # Actual code with line numbers and context
    ]
}
```

### Processing Flow Comparison by Query Type

| Processing Step | General Query | Semi-General Query | Focused Query |
|----------------|---------------|-------------------|---------------|
| **Query Classification** | 2-3s | 1-2s | 0.5-1s |
| **Context Retrieval** | 35-45s | 15-20s | 4-6s |
| **Semantic Analysis** | 8-12s | 4-6s | 1-2s |
| **Context Ranking** | 3-5s | 2-3s | 0.5-1s |
| **Package Generation** | 1-2s | 0.5-1s | 0.2-0.5s |
| **Total Processing** | 49-67s | 22.5-32s | 6-10.5s |

**Cross-References:**
- See [Codebase Indexing & Real-Time Processing](03_CODEBASE_INDEXING_REALTIME_PROCESSING.md) for indexing architecture
- See [Machine Learning & AI Models](08_MACHINE_LEARNING_AI_MODELS.md) for classification models
- See [Performance & Scalability](09_PERFORMANCE_SCALABILITY.md) for query optimization
